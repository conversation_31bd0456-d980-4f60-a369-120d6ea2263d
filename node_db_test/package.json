{"name": "node_db_test", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run clean && node esbuild.config.js", "build:watch": "node esbuild.config.js watch", "build:simple": "npm run clean && node esbuild.simple.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.14.0", "devDependencies": {"esbuild": "^0.25.9", "rimraf": "^6.0.1"}, "dependencies": {"dotenv": "^17.2.1", "express": "^5.1.0"}}