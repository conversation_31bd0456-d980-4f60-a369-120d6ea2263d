{"name": "node_db_test", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run clean && node esbuild.config.js", "build:watch": "node esbuild.config.js watch", "start": "node ./dist/index.js"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.14.0", "devDependencies": {"@types/node": "^24.3.0", "esbuild": "^0.25.9", "rimraf": "^6.0.1", "typescript": "^5.9.2"}, "dependencies": {"dotenv": "^17.2.1", "express": "^5.1.0"}}