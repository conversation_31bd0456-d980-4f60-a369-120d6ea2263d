import esbuild from "esbuild";
import { spawn } from "child_process";
import path from "path";

const config = {
  entryPoints: ["src/index.ts"],
  bundle: true,
  outdir: "dist",
  platform: "node",
  target: "node16",
  format: "esm",
  sourcemap: true,
  resolveExtensions: [".ts", ".js", ".json"],
  loader: {
    ".ts": "ts",
    ".js": "js",
    ".json": "json",
  },
  external: [],
  minify: false,
  splitting: false,
  metafile: true,
  logLevel: "info",
};

async function build() {
  try {
    const result = await esbuild.build(config);
    console.log("Build completed successfully!");
    if (result.metafile) {
      console.log("Metafile generated");
    }
  } catch (error) {
    console.error("Build failed:", error);
    process.exit(1);
  }
}

let childProcess = null;

function startProgram() {
  // Kill existing process if running
  if (childProcess) {
    console.log("Stopping previous instance...");
    childProcess.kill();
    childProcess = null;
  }

  const outputFile = path.join("dist", "index.js");
  childProcess = spawn("node", [outputFile], {
    stdio: "inherit",
    cwd: process.cwd(),
  });

  childProcess.on("error", (error) => {
    console.error("Failed to start program:", error);
  });

  childProcess.on("exit", (code, signal) => {
    if (signal !== "SIGTERM") {
      console.log(`Program exited with code ${code}`);
    }
    childProcess = null;
  });
}

async function watch() {
  const ctx = await esbuild.context({
    ...config,
    minify: false,
    plugins: [
      {
        name: "auto-restart",
        setup(build) {
          build.onEnd((result) => {
            if (result.errors.length === 0) {
              console.log("----------- Build completed successfully!");
              startProgram();
            } else {
              console.error("Build failed, not starting program");
            }
          });
        },
      },
    ],
  });

  await ctx.watch();
  console.log("Watching for changes...");

  // Handle graceful shutdown
  process.on("SIGINT", async () => {
    console.log("\nShutting down...");
    if (childProcess) {
      childProcess.kill();
    }
    await ctx.dispose();
    process.exit(0);
  });
}

export { config, build, watch };

// Check if this module is being run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const command = process.argv[2];

  if (command === "watch") {
    watch();
  } else {
    build();
  }
}
